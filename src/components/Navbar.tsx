
import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Menu, X } from "lucide-react";
import { useScrollPosition } from "@/hooks/useScrollPosition";
import { cn } from "@/lib/utils";

const Navbar = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const { isNavbarAtHeroBottom } = useScrollPosition();
  const [isVisible, setIsVisible] = useState(false);
  const [hasScrolledDown, setHasScrolledDown] = useState(false);
  const [prevScrollY, setPrevScrollY] = useState(0);

  // Track scroll direction and section visibility
  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      const scrollingDown = currentScrollY > prevScrollY;

      // Update scroll direction state
      setHasScrolledDown(scrollingDown);
      setPrevScrollY(currentScrollY);

      // Check if the navbar bottom aligns with the hero section bottom
      const shouldBeVisible = isNavbarAtHeroBottom();
      setIsVisible(shouldBeVisible);
    };

    window.addEventListener("scroll", handleScroll, { passive: true });

    // Call once to set initial values
    handleScroll();

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, [prevScrollY, isNavbarAtHeroBottom]);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  return (
    <nav
      className={cn(
        "fixed top-0 left-0 right-0 z-50 transition-all duration-500",
        isVisible
          ? "nav-appear"
          : "nav-disappear",
        // Apply stained glass effect when visible
        isVisible && "glass-morphism"
      )}
    >
      <div className="container mx-auto px-4 md:px-6 py-2">
        <div className="flex items-center justify-between h-16">
          <div className="flex items-center">
            <a href="#" className="flex items-center">
              <img
                src="/lovable-uploads/ad768163-427a-47fa-8859-c1c84d20a92a.png"
                alt="Tech Local Logo"
                className="h-8 md:h-10 w-auto transition-all duration-300 hover:scale-105 animate-fade-in drop-shadow-sm"
                style={{ animationDuration: "0.8s" }}
              />
            </a>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:block">
            <div className="flex items-center space-x-6">
              <a
                href="#about"
                className="text-techlocal-dark hover:text-black transition-all duration-300 relative group"
              >
                <span className="relative z-10">About</span>
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-techlocal-dark transition-all duration-300 group-hover:w-full"></span>
                <span className="absolute inset-0 -z-10 scale-75 opacity-0 blur-sm bg-white/30 rounded-full transition-all duration-300 group-hover:scale-100 group-hover:opacity-30"></span>
              </a>
              <a
                href="#services"
                className="text-techlocal-dark hover:text-black transition-all duration-300 relative group"
              >
                <span className="relative z-10">Services</span>
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-techlocal-dark transition-all duration-300 group-hover:w-full"></span>
                <span className="absolute inset-0 -z-10 scale-75 opacity-0 blur-sm bg-white/30 rounded-full transition-all duration-300 group-hover:scale-100 group-hover:opacity-30"></span>
              </a>
              <a href="#contact">
                <Button
                  variant="default"
                  className="bg-techlocal-dark hover:bg-black text-white transition-all duration-300 hover:scale-105 hover:shadow-lg"
                >
                  Get Started
                </Button>
              </a>
            </div>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              onClick={toggleMenu}
              className="inline-flex items-center justify-center p-2 rounded-md text-techlocal-dark hover:text-black focus:outline-none"
            >
              {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile Navigation */}
      {isMenuOpen && (
        <div className="md:hidden glass-morphism border-t">
          <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3">
            <a
              href="#about"
              className="block px-3 py-2 rounded-md text-base font-medium text-techlocal-dark hover:bg-white/30 transition-all duration-300 relative overflow-hidden group"
              onClick={toggleMenu}
            >
              <span className="relative z-10">About</span>
              <span className="absolute inset-0 -z-10 opacity-0 bg-white/20 transform translate-x-full transition-all duration-500 group-hover:translate-x-0 group-hover:opacity-100"></span>
            </a>
            <a
              href="#services"
              className="block px-3 py-2 rounded-md text-base font-medium text-techlocal-dark hover:bg-white/30 transition-all duration-300 relative overflow-hidden group"
              onClick={toggleMenu}
            >
              <span className="relative z-10">Services</span>
              <span className="absolute inset-0 -z-10 opacity-0 bg-white/20 transform translate-x-full transition-all duration-500 group-hover:translate-x-0 group-hover:opacity-100"></span>
            </a>
            <a
              href="#contact"
              className="block px-3 py-2 rounded-md text-base font-medium"
              onClick={toggleMenu}
            >
              <Button
                variant="default"
                className="w-full bg-techlocal-dark hover:bg-black text-white transition-all duration-300 hover:scale-105 hover:shadow-lg"
              >
                Get Started
              </Button>
            </a>
          </div>
        </div>
      )}
    </nav>
  );
};

export default Navbar;
