import { <PERSON><PERSON> } from "@/components/ui/button";
import { CircleUser, Briefcase, Globe, Code, Laptop, Server, Monitor, Smartphone, HeartHandshake, Network, Share, ChevronRight, Braces, BarChart } from "lucide-react";

const Hero = () => {
  const icons = [
    { Icon: Globe, position: "top-[10%] left-[8%]", size: 80 },
    { Icon: CircleUser, position: "top-[75%] left-[25%]", size: 70 },
    { Icon: Briefcase, position: "top-[15%] left-[88%]", size: 75 },
    { Icon: Code, position: "top-[92%] left-[60%]", size: 80 },
    { Icon: Laptop, position: "top-[43%] left-[20%]", size: 65 },
    { Icon: Server, position: "top-[5%] left-[30%]", size: 70 },
    { Icon: Monitor, position: "top-[35%] left-[75%]", size: 75 },
    { Icon: Smartphone, position: "top-[65%] left-[60%]", size: 60 },
    { Icon: HeartHandshake, position: "top-[20%] left-[55%]", size: 85 },
    { Icon: Network, position: "top-[65%] left-[95%]", size: 70 },
    { Icon: Share, position: "top-[80%] left-[75%]", size: 65 },
    { Icon: Braces, position: "top-[38%] left-[40%]", size: 75 },
    { Icon: BarChart, position: "top-[65%] left-[3%]", size: 80 },
  ];

  return (
    <section id="hero" className="relative overflow-hidden pt-6 pb-16 md:pt-8 md:pb-24">
      <div className="section-container">
        <div className="max-w-4xl mx-auto text-center">
          <div className="mt-0 mb-10 flex justify-center">
            <img
              src="/lovable-uploads/ad768163-427a-47fa-8859-c1c84d20a92a.png"
              alt="Tech Local Logo"
              className="h-12 md:h-20 w-auto"
            />
          </div>

          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 font-dm-serif tracking-tight animate-fade-in">
            Making connections
          </h1>

          <p className="text-lg md:text-xl mb-8 max-w-2xl mx-auto animate-fade-in" style={{ animationDelay: "0.2s" }}>
            Connecting local businesses to their customers through smart,
            accessible web solutions. We make technology simple, so you can
            focus on what you do best.
          </p>

          <div className="flex flex-col sm:flex-row justify-center gap-4 animate-fade-in" style={{ animationDelay: "0.4s" }}>
            <a href="#contact">
              <Button className="text-lg px-8 py-6 bg-techlocal-dark hover:bg-black text-white">
                Get Started
              </Button>
            </a>
            <a href="#services">
              <Button variant="outline" className="text-lg px-8 py-6 border-techlocal-dark text-techlocal-dark hover:bg-techlocal-stone/20">
                Our Services
              </Button>
            </a>
          </div>
        </div>
      </div>

      {/* Static, non-interactive icons background with more spread out positioning */}
      <div className="absolute inset-0 -z-10 overflow-hidden">
        {icons.map((icon, index) => (
          <div
            key={index}
            className={`absolute ${icon.position} opacity-10`}
          >
            <icon.Icon size={icon.size} strokeWidth={1} className="text-techlocal-dark" />
          </div>
        ))}
      </div>
    </section>
  );
};

export default Hero;
