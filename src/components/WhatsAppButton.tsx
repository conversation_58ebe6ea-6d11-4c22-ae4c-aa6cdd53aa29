import React, { useState, useEffect } from 'react';

// WhatsApp SVG Icon Component
const WhatsAppIcon = ({ size = 24 }: { size?: number }) => (
  <svg 
    width={size} 
    height={size} 
    viewBox="0 0 24 24" 
    fill="currentColor"
  >
    <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.465 3.516"/>
  </svg>
);

const WhatsAppButton = () => {
  const [showPulse, setShowPulse] = useState(false);
  const [lastScrollTime, setLastScrollTime] = useState(Date.now());

  // WhatsApp number without spaces and special characters for URL
  const whatsappNumber = "27689084966";
  const whatsappUrl = `https://wa.me/${whatsappNumber}?text=Hi! I'd like to know more about your services.`;

  useEffect(() => {
    let pulseInterval: NodeJS.Timeout;

    const handleScroll = () => {
      const now = Date.now();
      setLastScrollTime(now);
      
      // Clear existing interval
      if (pulseInterval) {
        clearInterval(pulseInterval);
      }
      
      // Start new interval for pulse animation
      pulseInterval = setInterval(() => {
        const timeSinceLastScroll = Date.now() - lastScrollTime;
        
        // Show pulse if user has been scrolling for at least 3 seconds
        if (timeSinceLastScroll >= 3000) {
          setShowPulse(true);
          setTimeout(() => setShowPulse(false), 2000); // Hide pulse after 2 seconds
        }
      }, 3000);
    };

    window.addEventListener('scroll', handleScroll);
    
    // Initial setup
    handleScroll();

    return () => {
      window.removeEventListener('scroll', handleScroll);
      if (pulseInterval) {
        clearInterval(pulseInterval);
      }
    };
  }, [lastScrollTime]);

  return (
    <div className="fixed bottom-6 right-6 z-50">
      <div className="relative">
        {/* Pulse animation ring */}
        {showPulse && (
          <div className="absolute inset-0 rounded-full bg-green-500 opacity-30 animate-ping"></div>
        )}
        
        {/* WhatsApp button */}
        <a
          href={whatsappUrl}
          target="_blank"
          rel="noopener noreferrer"
          className="flex items-center justify-center w-14 h-14 bg-green-500 hover:bg-green-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110"
          aria-label="Chat with us on WhatsApp"
        >
          <WhatsAppIcon size={28} />
        </a>
        
        {/* Tooltip */}
        <div className="absolute bottom-full right-0 mb-2 px-3 py-1 bg-gray-800 text-white text-sm rounded-lg opacity-0 hover:opacity-100 transition-opacity duration-300 whitespace-nowrap pointer-events-none">
          Chat with us on WhatsApp
        </div>
      </div>
    </div>
  );
};

export default WhatsAppButton; 