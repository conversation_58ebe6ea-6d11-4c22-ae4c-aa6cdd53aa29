import { Mail, Phone, MapPin, ShieldCheck } from "lucide-react";
import { Link } from "react-router-dom";

const Footer = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-techlocal-dark text-white">
      <div className="container mx-auto px-4 py-12 md:px-6">
        <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
          <div className="space-y-4">
            <h3 className="text-xl font-bold font-dm-serif">Tech Local</h3>
            <p className="max-w-xs">
              Making connections between local businesses and their customers through smart, 
              accessible web solutions.
            </p>
          </div>

          <div className="space-y-4">
            <h3 className="text-lg font-bold font-dm-serif">Quick Links</h3>
            <ul className="space-y-2">
              <li>
                <a href="#" className="hover:text-techlocal-beige transition-colors">
                  Home
                </a>
              </li>
              <li>
                <a href="#about" className="hover:text-techlocal-beige transition-colors">
                  About Us
                </a>
              </li>
              <li>
                <a href="#services" className="hover:text-techlocal-beige transition-colors">
                  Services
                </a>
              </li>
              <li>
                <a href="#contact" className="hover:text-techlocal-beige transition-colors">
                  Contact
                </a>
              </li>
            </ul>
          </div>

          <div className="space-y-4">
            <h3 className="text-lg font-bold font-dm-serif">Contact Us</h3>
            <ul className="space-y-2">
              <li className="flex items-center">
                <Mail size={18} className="mr-2" />
                <a href="mailto:<EMAIL>" className="hover:text-techlocal-beige transition-colors">
                  <EMAIL>
                </a>
              </li>
              <li className="flex items-center">
                <Phone size={18} className="mr-2" />
                <span>+27 (0) 68 908 4966</span>
              </li>
              <li className="flex items-start">
                <MapPin size={18} className="mr-2 mt-1" />
                <span>East London, South Africa</span>
              </li>
            </ul>
          </div>
        </div>

        <div className="mt-12 pt-8 border-t border-gray-700 flex flex-col md:flex-row justify-between items-center">
          <p className="text-center text-sm mb-4 md:mb-0">
            &copy; {currentYear} Tech Local (Pty) Ltd, an SDG company. All rights reserved.
          </p>
          <div className="flex items-center">
            <Link to="/admin" className="flex items-center text-sm text-gray-400 hover:text-white transition-colors">
              <ShieldCheck size={16} className="mr-1" />
              Admin Login
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
